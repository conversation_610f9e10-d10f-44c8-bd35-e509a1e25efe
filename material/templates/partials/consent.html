{#-
  This file was automatically generated - do not edit
-#}
{% macro render_cookie(cookie, type) %}
  {% set checked = "" %}
  {% if cookie is string %}
    {% set name = cookie %}
    {% set checked = "checked" %}
  {% else %}
    {% set name = cookie.name %}
    {% if cookie.checked %}
      {% set checked = "checked" %}
    {% endif %}
  {% endif %}
  <li class="task-list-item">
    <label class="task-list-control">
      <input type="checkbox" name="{{ type }}" {{ checked }}>
      <span class="task-list-indicator"></span>
      {{ name }}
    </label>
  </li>
{% endmacro %}
{% set actions = config.extra.consent.actions %}
{% if not actions %}
  {% set actions = ["accept", "manage"] %}
{% endif %}
{% if "manage" not in actions %}
  {% set checked = "checked" %}
{% endif %}
<h4>{{ config.extra.consent.title }}</h4>
<p>{{ config.extra.consent.description }}</p>
<input class="md-toggle" type="checkbox" id="__settings" {{ checked }}>
<div class="md-consent__settings">
  <ul class="task-list">
    {% set cookies = config.extra.consent.cookies %}
    {% if "analytics" not in cookies %}
      {% if config.extra.analytics %}
        {{ render_cookie("Google Analytics", "analytics") }}
      {% endif %}
    {% endif %}
    {% if "github" not in cookies %}
      {% if config.repo_url and "github.com" in config.repo_url %}
        {{ render_cookie("GitHub", "github") }}
      {% endif %}
    {% endif %}
    {% for type in cookies %}
      {% if cookies[type] %}
        {{ render_cookie(cookies[type], type) }}
      {% endif %}
    {% endfor %}
  </ul>
</div>
<div class="md-consent__controls">
  {% for action in actions %}
    {% if action == "accept" %}
      <button class="md-button md-button--primary">
        {{- lang.t("consent.accept") -}}
      </button>
    {% endif %}
    {% if action == "reject" %}
      <button type="reset" class="md-button md-button--primary">
        {{- lang.t("consent.reject") -}}
      </button>
    {% endif %}
    {% if action == "manage" %}
      <label class="md-button" for="__settings">
        {{- lang.t("consent.manage") -}}
      </label>
    {% endif %}
  {% endfor %}
</div>
