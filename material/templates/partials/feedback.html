{#-
  This file was automatically generated - do not edit
-#}
{% if config.extra.analytics %}
  {% set feedback = config.extra.analytics.feedback %}
{% endif %}
{% if page.meta and page.meta.hide %}
  {% if "feedback" in page.meta.hide %}
    {% set feedback = None %}
  {% endif %}
{% endif %}
{% if feedback %}
  <form class="md-feedback" name="feedback" hidden>
    <fieldset>
      <legend class="md-feedback__title">
        {{ feedback.title }}
      </legend>
      <div class="md-feedback__inner">
        <div class="md-feedback__list">
          {% for rating in feedback.ratings %}
            <button class="md-feedback__icon md-icon" type="submit" title="{{ rating.name }}" data-md-value="{{ rating.data }}">
              {% include ".icons/" ~ rating.icon ~ ".svg" %}
            </button>
          {% endfor %}
        </div>
        <div class="md-feedback__note">
          {% for rating in feedback.ratings %}
            <div data-md-value="{{ rating.data }}" hidden>
              {% set url = "/" ~ page.url %}
              {% if page.meta and page.meta.title %}
                {% set title = page.meta.title | urlencode %}
              {% else %}
                {% set title = page.title | urlencode %}
              {% endif %}
              {% set note = rating.note %}
              {% if note %}
                {% set note = note | replace("{url}", url) %}
                {% set note = note | replace("{title}", title) %}
              {% endif %}
              {{ note }}
            </div>
          {% endfor %}
        </div>
      </div>
    </fieldset>
  </form>
{% endif %}
