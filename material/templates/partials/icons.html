{#-
  This file was automatically generated - do not edit
-#}
{% if config.theme.icon.admonition %}
  {% set _ = namespace(style = "\u003cstyle\u003e:root{") %}
  {% for type, icon in config.theme.icon.admonition | items %}
    {% if type and icon %}
      {% import ".icons/" ~ icon ~ ".svg" as icon %}
      {% set _.style = _.style ~
        "--md-admonition-icon--" ~ type ~ ":" ~
        "url('data:image/svg+xml;charset=utf-8," ~
          icon | replace("\n", "") | urlencode ~
        "');"
      %}
    {% endif %}
  {% endfor %}
  {% set _.style = _.style ~ "}\u003c/style\u003e" %}
  {{ _.style }}
{% endif %}
{% if config.theme.icon.annotation %}
  {% set _ = namespace(style = "\u003cstyle\u003e:root{") %}
  {% import ".icons/" ~ config.theme.icon.annotation ~ ".svg" as icon %}
  {% set _.style = _.style ~
    "--md-annotation-icon:" ~
    "url('data:image/svg+xml;charset=utf-8," ~
      icon | replace("\n", "") | urlencode ~
    "');"
  %}
  {% set _.style = _.style ~ "}\u003c/style\u003e" %}
  {{ _.style }}
{% endif %}
{% if config.theme.icon.tag %}
  {% set _ = namespace(style = "\u003cstyle\u003e:root{") %}
  {% for type, icon in config.theme.icon.tag | items %}
    {% if type and icon %}
      {% import ".icons/" ~ icon ~ ".svg" as icon %}
      {% if type != "default" %}
        {% set modifier = ".md-tag--" ~ type %}
      {% endif %}
      {% set _.style = _.style ~
        ".md-tag" ~ modifier ~ "{" ~
          "--md-tag-icon:" ~
          "url('data:image/svg+xml;charset=utf-8," ~
            icon | replace("\n", "") | urlencode ~
          "');" ~
        "}"
      %}
    {% endif %}
  {% endfor %}
  {% set _.style = _.style ~ "}\u003c/style\u003e" %}
  {{ _.style }}
{% endif %}
