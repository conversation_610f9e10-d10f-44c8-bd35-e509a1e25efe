{"$schema": "https://json-schema.org/draft-07/schema", "title": "Built-in offline plugin", "oneOf": [{"markdownDescription": "https://squidfunk.github.io/mkdocs-material/plugins/offline/", "const": "offline"}, {"type": "object", "properties": {"offline": {"markdownDescription": "https://squidfunk.github.io/mkdocs-material/plugins/offline/", "type": "object", "properties": {"enabled": {"title": "Enable plugin", "markdownDescription": "https://squidfunk.github.io/mkdocs-material/plugins/offline/#config.enabled", "type": "boolean", "default": true}}, "additionalProperties": false}}, "additionalProperties": false}]}