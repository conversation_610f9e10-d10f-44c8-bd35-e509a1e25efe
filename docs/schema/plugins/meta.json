{"$schema": "https://json-schema.org/draft-07/schema", "title": "Built-in meta plugin", "oneOf": [{"markdownDescription": "https://squidfunk.github.io/mkdocs-material/plugins/meta/", "const": "meta"}, {"type": "object", "properties": {"meta": {"markdownDescription": "https://squidfunk.github.io/mkdocs-material/plugins/meta/", "type": "object", "properties": {"enabled": {"title": "Enable plugin", "markdownDescription": "https://squidfunk.github.io/mkdocs-material/plugins/meta/#config.enabled", "type": "boolean", "default": true}, "meta_file": {"title": "Meta file name", "markdownDescription": "https://squidfunk.github.io/mkdocs-material/plugins/meta/#config.meta_file", "pattern": "\\.yml$", "default": "\"**/.meta.yml\""}}, "additionalProperties": false}}, "additionalProperties": false}]}