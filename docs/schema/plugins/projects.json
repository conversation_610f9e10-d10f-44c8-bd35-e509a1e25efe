{"$schema": "https://json-schema.org/draft-07/schema", "title": "Built-in projects plugin", "oneOf": [{"markdownDescription": "https://squidfunk.github.io/mkdocs-material/plugins/projects/", "const": "projects"}, {"type": "object", "properties": {"projects": {"markdownDescription": "https://squidfunk.github.io/mkdocs-material/plugins/projects/", "type": "object", "properties": {"enabled": {"title": "Enable plugin", "markdownDescription": "https://squidfunk.github.io/mkdocs-material/plugins/projects/#config.enabled", "type": "boolean", "default": true}, "concurrency": {"title": "Concurrency (number of CPUs)", "markdownDescription": "https://squidfunk.github.io/mkdocs-material/plugins/projects/#config.concurrency", "type": "number"}, "cache": {"title": "Enable caching", "markdownDescription": "https://squidfunk.github.io/mkdocs-material/plugins/projects/#config.cache", "type": "boolean", "default": true}, "cache_dir": {"title": "Cache directory", "markdownDescription": "https://squidfunk.github.io/mkdocs-material/plugins/projects/#config.cache_dir", "type": "string", "default": ".cache/plugins/projects"}, "log": {"title": "Enable logging", "markdownDescription": "https://squidfunk.github.io/mkdocs-material/plugins/projects/#config.log", "type": "boolean", "default": true}, "log_level": {"title": "Log level", "markdownDescription": "https://squidfunk.github.io/mkdocs-material/plugins/projects/#config.log_level", "enum": ["error", "warn", "info", "debug"], "default": "info"}, "projects": {"title": "Enable projects", "markdownDescription": "https://squidfunk.github.io/mkdocs-material/plugins/projects/#config.projects", "type": "boolean", "default": true}, "projects_dir": {"title": "Projects directory", "markdownDescription": "https://squidfunk.github.io/mkdocs-material/plugins/projects/#config.projects_dir", "type": "string", "default": "projects"}, "projects_config_files": {"title": "Projects configuration files", "markdownDescription": "https://squidfunk.github.io/mkdocs-material/plugins/projects/#config.projects_config_files", "type": "string", "default": "\"*/mkdocs.yml\""}, "projects_config_transform": {"title": "Projects configuration transform", "markdownDescription": "https://squidfunk.github.io/mkdocs-material/plugins/projects/#config.projects_config_transform", "type": "string", "defaultSnippets": [{"body": "!!python/name:${1:module}.${2:function}"}]}, "hoisting": {"title": "Enable hoisting", "markdownDescription": "https://squidfunk.github.io/mkdocs-material/plugins/projects/#config.hoisting", "type": "boolean", "default": true}}, "additionalProperties": false}}, "additionalProperties": false}]}