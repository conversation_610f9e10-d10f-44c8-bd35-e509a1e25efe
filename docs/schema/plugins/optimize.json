{"$schema": "https://json-schema.org/draft-07/schema", "title": "Built-in optimize plugin", "oneOf": [{"markdownDescription": "https://squidfunk.github.io/mkdocs-material/plugins/optimize/", "const": "optimize"}, {"type": "object", "properties": {"optimize": {"markdownDescription": "https://squidfunk.github.io/mkdocs-material/plugins/optimize/", "type": "object", "properties": {"enabled": {"title": "Enable plugin", "markdownDescription": "https://squidfunk.github.io/mkdocs-material/plugins/optimize/#config.enabled", "type": "boolean", "default": true}, "concurrency": {"title": "Concurrency (number of CPUs)", "markdownDescription": "https://squidfunk.github.io/mkdocs-material/plugins/optimize/#config.concurrency", "type": "number"}, "cache": {"title": "Enable caching", "markdownDescription": "https://squidfunk.github.io/mkdocs-material/plugins/optimize/#config.cache", "type": "boolean", "default": true}, "cache_dir": {"title": "Cache directory", "markdownDescription": "https://squidfunk.github.io/mkdocs-material/plugins/optimize/#config.cache_dir", "type": "string", "default": ".cache/plugins/optimize"}, "optimize_png": {"title": "Optimization of PNGs", "markdownDescription": "https://squidfunk.github.io/mkdocs-material/plugins/optimize/#config.optimize_png", "type": "boolean", "default": true}, "optimize_png_speed": {"title": "Speed/quality tradeoff [1,10]", "markdownDescription": "https://squidfunk.github.io/mkdocs-material/plugins/optimize/#config.optimize_png_speed", "type": "number", "default": 4}, "optimize_png_strip": {"title": "Strip unnecessary metadata from PNGs", "markdownDescription": "https://squidfunk.github.io/mkdocs-material/plugins/optimize/#config.optimize_png_strip", "type": "boolean", "default": true}, "optimize_jpg": {"title": "Optimization of JPGs", "markdownDescription": "https://squidfunk.github.io/mkdocs-material/plugins/optimize/#config.optimize_jpg", "type": "boolean", "default": true}, "optimize_jpg_quality": {"title": "Speed/quality tradeoff for pngquant [0,10]", "markdownDescription": "https://squidfunk.github.io/mkdocs-material/plugins/optimize/#config.optimize_jpg_quality", "type": "number", "default": 60}, "optimize_jpg_progressive": {"title": "Progressive encoding (faster rendering)", "markdownDescription": "https://squidfunk.github.io/mkdocs-material/plugins/optimize/#config.optimize_jpg_progressive", "type": "boolean", "default": true}, "optimize_include": {"title": "Files or folders to include", "markdownDescription": "https://squidfunk.github.io/mkdocs-material/plugins/optimize/#config.optimize_include", "type": "array", "items": {"title": "Files or folders matching this pattern will be included", "markdownDescription": "https://squidfunk.github.io/mkdocs-material/plugins/optimize/#config.optimize_include", "pattern": ".*"}, "uniqueItems": true, "minItems": 1}, "optimize_exclude": {"title": "Files or folders to exclude", "markdownDescription": "https://squidfunk.github.io/mkdocs-material/plugins/optimize/#config.optimize_exclude", "type": "array", "items": {"title": "Files or folders matching this pattern will be excluded", "markdownDescription": "https://squidfunk.github.io/mkdocs-material/plugins/optimize/#config.optimize_exclude", "pattern": ".*"}, "uniqueItems": true, "minItems": 1}, "print_gain": {"title": "Print optimization gain", "markdownDescription": "https://squidfunk.github.io/mkdocs-material/plugins/optimize/#config.print_gain", "type": "boolean", "default": true}, "print_gain_summary": {"title": "Print optimization gain summary", "markdownDescription": "https://squidfunk.github.io/mkdocs-material/plugins/optimize/#config.print_gain_summary", "type": "boolean", "default": true}}, "additionalProperties": false}}, "additionalProperties": false}]}