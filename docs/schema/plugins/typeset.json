{"$schema": "https://json-schema.org/draft-07/schema", "title": "Built-in typeset plugin", "oneOf": [{"markdownDescription": "https://squidfunk.github.io/mkdocs-material/plugins/typeset/", "const": "typeset"}, {"type": "object", "properties": {"typeset": {"markdownDescription": "https://squidfunk.github.io/mkdocs-material/plugins/typeset/", "type": "object", "properties": {"enabled": {"title": "Enable plugin", "markdownDescription": "https://squidfunk.github.io/mkdocs-material/plugins/typeset/#config.enabled", "type": "boolean", "default": true}}, "additionalProperties": false}}, "additionalProperties": false}]}