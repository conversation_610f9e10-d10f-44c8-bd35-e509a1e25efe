{"$schema": "https://json-schema.org/draft-07/schema", "title": "Built-in info plugin", "oneOf": [{"markdownDescription": "https://squidfunk.github.io/mkdocs-material/plugins/info/", "const": "info"}, {"type": "object", "properties": {"info": {"markdownDescription": "https://squidfunk.github.io/mkdocs-material/plugins/info/", "type": "object", "properties": {"enabled": {"title": "Enable plugin", "markdownDescription": "https://squidfunk.github.io/mkdocs-material/plugins/info/#config.enabled", "type": "boolean", "default": true}, "enabled_on_serve": {"title": "Enable plugin when previewing", "markdownDescription": "https://squidfunk.github.io/mkdocs-material/plugins/info/#config.enabled_on_serve", "type": "boolean", "default": false}, "archive": {"title": "Enable creation of archive", "markdownDescription": "https://squidfunk.github.io/mkdocs-material/plugins/info/#config.archive", "type": "boolean", "default": true}, "archive_stop_on_violation": {"title": "Stop creation of archive on violation", "markdownDescription": "https://squidfunk.github.io/mkdocs-material/plugins/info/#config.archive_stop_on_violation", "type": "boolean", "default": true}}, "additionalProperties": false}}, "additionalProperties": false}]}