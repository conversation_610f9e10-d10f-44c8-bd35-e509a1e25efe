{"$schema": "https://json-schema.org/draft-07/schema", "title": "Built-in group plugin", "oneOf": [{"type": "object", "properties": {"group": {"markdownDescription": "https://squidfunk.github.io/mkdocs-material/plugins/group/", "type": "object", "properties": {"enabled": {"title": "Enable plugin", "markdownDescription": "https://squidfunk.github.io/mkdocs-material/plugins/group/#config.enabled", "type": "boolean", "default": true}, "plugins": {"markdownDescription": "https://squidfunk.github.io/mkdocs-material/plugins/group/#config.plugins", "$ref": "../plugins.json"}}, "additionalProperties": false}}, "additionalProperties": false}]}