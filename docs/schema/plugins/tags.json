{"$schema": "https://json-schema.org/draft-07/schema", "title": "Built-in tags plugin", "oneOf": [{"markdownDescription": "https://squidfunk.github.io/mkdocs-material/plugins/tags/", "const": "tags"}, {"type": "object", "properties": {"tags": {"markdownDescription": "https://squidfunk.github.io/mkdocs-material/plugins/tags/", "type": "object", "properties": {"enabled": {"title": "Enable plugin", "markdownDescription": "https://squidfunk.github.io/mkdocs-material/plugins/tags/#config.enabled", "type": "boolean", "default": true}, "tags": {"title": "Rendering of tags", "markdownDescription": "https://squidfunk.github.io/mkdocs-material/plugins/tags/#config.tags", "type": "boolean", "default": true}, "tags_file": {"title": "Markdown file to render tags index", "markdownDescription": "https://squidfunk.github.io/mkdocs-material/plugins/tags/#config.tags_file", "pattern": "\\.md$", "default": "tags.md"}, "tags_slugify": {"title": "Tags slugify function", "markdownDescription": "https://squidfunk.github.io/mkdocs-material/plugins/tags/#config.tags_slugify", "default": "!!python/object/apply:pymdownx.slugs.slugify {kwds: {case: lower}}"}, "tags_slugify_separator": {"title": "Tags slugify separator", "markdownDescription": "https://squidfunk.github.io/mkdocs-material/plugins/tags/#config.tags_slugify_separator", "type": "string", "default": "-"}, "tags_slugify_format": {"title": "Format string for tags slugifier", "markdownDescription": "https://squidfunk.github.io/mkdocs-material/plugins/tags/#config.tags_slugify_format", "type": "string", "default": "\"tag:{slug}\""}, "tags_hierarchy": {"title": "Rendering of tags", "markdownDescription": "https://squidfunk.github.io/mkdocs-material/plugins/tags/#config.tags", "type": "boolean", "default": true}, "tags_sort_by": {"title": "Sort tags by this function", "markdownDescription": "https://squidfunk.github.io/mkdocs-material/plugins/tags/#config.tags_sort_by", "default": "!!python/name:material.plugins.tags.casefold"}, "tags_sort_reverse": {"title": "Soft tags in reverse", "markdownDescription": "https://squidfunk.github.io/mkdocs-material/plugins/tags/#config.tags_sort_reverse", "default": false}, "tags_name_property": {"title": "Tags front matter property", "markdownDescription": "https://squidfunk.github.io/mkdocs-material/plugins/tags/#config.tags_name_property", "type": "string", "default": "tags"}, "tags_name_variable": {"title": "Tags Jinja variable name", "markdownDescription": "https://squidfunk.github.io/mkdocs-material/plugins/tags/#config.tags_name_variable", "type": "string", "default": "tags"}, "tags_allowed": {"title": "Tags allowed", "markdownDescription": "https://squidfunk.github.io/mkdocs-material/plugins/tags/#config.tags_allowed", "type": "array", "items": {"type": "string"}, "uniqueItems": true, "default": []}, "listings": {"title": "Rendering of listings", "markdownDescription": "https://squidfunk.github.io/mkdocs-material/plugins/tags/#config.listings", "type": "boolean", "default": true}, "listings_map": {"title": "Map of listing configurations", "markdownDescription": "https://squidfunk.github.io/mkdocs-material/plugins/tags/#config.listings_map", "type": "object", "patternProperties": {".*": {"markdownDescription": "https://squidfunk.github.io/mkdocs-material/plugins/tags/#listing-configuration", "type": "object", "properties": {"scope": {"title": "Scoped listing", "markdownDescription": "https://squidfunk.github.io/mkdocs-material/plugins/tags/#listing.scope", "default": false}, "shadow": {"title": "Render shadow tags", "markdownDescription": "https://squidfunk.github.io/mkdocs-material/plugins/tags/#listing.shadow", "default": true}, "include": {"title": "Tag inclusion list", "markdownDescription": "https://squidfunk.github.io/mkdocs-material/plugins/tags/#listing.include", "type": "array", "uniqueItems": true}, "exclude": {"title": "Tag exclusion list", "markdownDescription": "https://squidfunk.github.io/mkdocs-material/plugins/tags/#listing.exclude", "type": "array", "uniqueItems": true}, "toc": {"title": "Render tags in table of contents", "markdownDescription": "https://squidfunk.github.io/mkdocs-material/plugins/tags/#listing.toc", "default": true}}}}, "additionalProperties": false}, "listings_sort_by": {"title": "Sort listing items by this function", "markdownDescription": "https://squidfunk.github.io/mkdocs-material/plugins/tags/#config.listings_sort_by", "default": "!!python/name:material.plugins.tags.casefold"}, "listings_sort_reverse": {"title": "Soft listing items in reverse", "markdownDescription": "https://squidfunk.github.io/mkdocs-material/plugins/tags/#config.listings_sort_reverse", "default": false}, "listings_tags_sort_by": {"title": "Sort listing tags by this function", "markdownDescription": "https://squidfunk.github.io/mkdocs-material/plugins/tags/#config.listings_tags_sort_by", "default": "!!python/name:material.plugins.tags.casefold"}, "listings_tags_sort_reverse": {"title": "Soft listing tags in reverse", "markdownDescription": "https://squidfunk.github.io/mkdocs-material/plugins/tags/#config.listings_tags_sort_reverse", "default": false}, "listings_directive": {"title": "Listings directive", "markdownDescription": "https://squidfunk.github.io/mkdocs-material/plugins/tags/#config.listings_directive", "type": "string", "default": "material/tags"}, "listings_toc": {"title": "Render tags in table of contents", "markdownDescription": "https://squidfunk.github.io/mkdocs-material/plugins/tags/#config.listings_toc", "default": true}, "shadow": {"title": "Rendering shadow tags on build", "markdownDescription": "https://squidfunk.github.io/mkdocs-material/plugins/tags/#config.shadow", "type": "boolean", "default": false}, "shadow_on_serve": {"title": "Rendering shadow tags on serve", "markdownDescription": "https://squidfunk.github.io/mkdocs-material/plugins/tags/#config.shadow_on_serve", "type": "boolean", "default": true}, "shadow_tags": {"title": "Shadow tags", "markdownDescription": "https://squidfunk.github.io/mkdocs-material/plugins/tags/#config.shadow_tags", "type": "array", "items": {"type": "string"}, "uniqueItems": true, "default": []}, "shadow_tags_prefix": {"title": "Shadow tags prefix", "markdownDescription": "https://squidfunk.github.io/mkdocs-material/plugins/tags/#config.shadow_tags_prefix", "type": "string", "default": "_"}, "shadow_tags_suffix": {"title": "Shadow tags suffix", "markdownDescription": "https://squidfunk.github.io/mkdocs-material/plugins/tags/#config.shadow_tags_suffix", "type": "string"}, "export": {"title": "Tags export", "markdownDescription": "https://squidfunk.github.io/mkdocs-material/plugins/tags/#config.export", "type": "boolean", "default": true}, "export_file": {"title": "Tags file", "markdownDescription": "https://squidfunk.github.io/mkdocs-material/plugins/tags/#config.export_file", "type": "boolean", "default": "tags.json"}, "export_only": {"title": "Only export tags, don't render them", "markdownDescription": "https://squidfunk.github.io/mkdocs-material/plugins/tags/#config.export_only", "type": "boolean", "default": true}}, "additionalProperties": false}}, "additionalProperties": false}]}