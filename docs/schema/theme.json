{"$schema": "https://json-schema.org/draft-07/schema", "title": "Theme configuration", "markdownDescription": "https://squidfunk.github.io/mkdocs-material/", "type": "object", "properties": {"name": {"title": "Theme name", "markdownDescription": "https://www.mkdocs.org/user-guide/configuration/#name", "oneOf": [{"const": "material"}, {"type": "null"}], "default": "material"}, "custom_dir": {"title": "Directory with theme overrides", "markdownDescription": "https://squidfunk.github.io/mkdocs-material/customization/#setup-and-theme-structure", "type": "string", "defaultSnippets": [{"body": "${1:overrides}"}]}, "static_templates": {"title": "Static templates to render", "markdownDescription": "https://www.mkdocs.org/user-guide/configuration/#static_templates", "type": "array", "items": {"title": "Path to HTML file", "markdownDescription": "https://www.mkdocs.org/user-guide/configuration/#static_templates", "pattern": "\\.html$"}, "uniqueItems": true, "minItems": 1}, "language": {"title": "Site language", "markdownDescription": "https://squidfunk.github.io/mkdocs-material/setup/changing-the-language/", "oneOf": [{"title": "Site language: Custom", "markdownDescription": "https://squidfunk.github.io/mkdocs-material/setup/changing-the-language/#custom-translations", "const": "custom"}, {"title": "Site language: Afrikaans", "const": "af"}, {"title": "Site language: Arabic", "const": "ar"}, {"title": "Site language: Bulgarian", "const": "bg"}, {"title": "Site language: Bengali (Bangla)", "const": "bn"}, {"title": "Site language: Catalan", "const": "ca"}, {"title": "Site language: Czech", "const": "cs"}, {"title": "Site language: Danish", "const": "da"}, {"title": "Site language: German", "const": "de"}, {"title": "Site language: Greek", "const": "el"}, {"title": "Site language: English", "const": "en"}, {"title": "Site language: Esperanto", "const": "eo"}, {"title": "Site language: Spanish", "const": "es"}, {"title": "Site language: Estonian", "const": "et"}, {"title": "Site language: Persian (Farsi)", "const": "fa"}, {"title": "Site language: Finnish", "const": "fi"}, {"title": "Site language: French", "const": "fr"}, {"title": "Site language: Galician", "const": "gl"}, {"title": "Site language: Hebrew", "const": "he"}, {"title": "Site language: Hindi", "const": "hi"}, {"title": "Site language: Croatian", "const": "hr"}, {"title": "Site language: Hungarian", "const": "hu"}, {"title": "Site language: Armenian", "const": "hy"}, {"title": "Site language: Indonesian", "const": "id"}, {"title": "Site language: Icelandic", "const": "is"}, {"title": "Site language: Italian", "const": "it"}, {"title": "Site language: Japanese", "const": "ja"}, {"title": "Site language: Georgian", "const": "ka"}, {"title": "Site language: Kannada", "const": "kn"}, {"title": "Site language: Korean", "const": "ko"}, {"title": "Site language: Lithuanian", "const": "lt"}, {"title": "Site language: Latvian", "const": "lv"}, {"title": "Site language: Macedonian", "const": "mk"}, {"title": "Site language: Mongolian", "const": "mn"}, {"title": "Site language: Bahasa Malaysia", "const": "ms"}, {"title": "Site language: Burmese", "const": "my"}, {"title": "Site language: Dutch", "const": "nl"}, {"title": "Site language: Norwegian (Bokmål)", "const": "nb"}, {"title": "Site language: Norwegian (Nynorsk)", "const": "nn"}, {"title": "Site language: Polish", "const": "pl"}, {"title": "Site language: Portuguese", "const": "pt"}, {"title": "Site language: Portuguese (Brasilian)", "const": "pt-BR"}, {"title": "Site language: Romanian", "const": "ro"}, {"title": "Site language: Russian", "const": "ru"}, {"title": "Site language: Sanskrit", "const": "sa"}, {"title": "Site language: Serbo-Croatian", "const": "sh"}, {"title": "Site language: Sinhalese", "const": "si"}, {"title": "Site language: Slovak", "const": "sk"}, {"title": "Site language: Slovenian", "const": "sl"}, {"title": "Site language: Albanian", "const": "sq"}, {"title": "Site language: Serbian", "const": "sr"}, {"title": "Site language: Swedish", "const": "sv"}, {"title": "Site language: Tamil", "const": "ta"}, {"title": "Site language: Telugu", "const": "te"}, {"title": "Site language: Thai", "const": "th"}, {"title": "Site language: Tagalog", "const": "tl"}, {"title": "Site language: Turkish", "const": "tr"}, {"title": "Site language: Ukrainian", "const": "uk"}, {"title": "Site language: Urdu", "const": "ur"}, {"title": "Site language: Uzbek", "const": "uz"}, {"title": "Site language: Vietnamese", "const": "vi"}, {"title": "Site language: Chinese (Simplified)", "const": "zh"}, {"title": "Site language: Chinese (Traditional)", "const": "zh-Han<PERSON>"}, {"title": "Site language: Chinese (Taiwanese)", "const": "zh-TW"}], "default": "en"}, "direction": {"title": "Directionality", "markdownDescription": "https://squidfunk.github.io/mkdocs-material/setup/changing-the-language/#directionality", "enum": ["ltr", "rtl"]}, "favicon": {"title": "Favicon", "markdownDescription": "https://squidfunk.github.io/mkdocs-material/setup/changing-the-logo-and-icons/#favicon", "pattern": "\\.(ico|png|svg|jpe?g|gif)$", "defaultSnippets": [{"body": "${1:path/to/file}.png"}]}, "logo": {"title": "Logo", "markdownDescription": "https://squidfunk.github.io/mkdocs-material/setup/changing-the-logo-and-icons/#logo", "pattern": "\\.(png|svg|jpe?g|gif|webp)$", "defaultSnippets": [{"body": "${1:path/to/file}.png"}]}, "icon": {"title": "Icons", "markdownDescription": "https://squidfunk.github.io/mkdocs-material/setup/changing-the-logo-and-icons/", "type": "object", "properties": {"edit": {"$ref": "#/$defs/icon"}, "view": {"$ref": "#/$defs/icon"}, "logo": {"$ref": "#/$defs/icon"}, "repo": {"$ref": "#/$defs/icon"}, "annotation": {"$ref": "#/$defs/icon"}, "top": {"$ref": "#/$defs/icon"}, "share": {"$ref": "#/$defs/icon"}, "menu": {"$ref": "#/$defs/icon"}, "alternate": {"$ref": "#/$defs/icon"}, "search": {"$ref": "#/$defs/icon"}, "close": {"$ref": "#/$defs/icon"}, "previous": {"$ref": "#/$defs/icon"}, "next": {"$ref": "#/$defs/icon"}, "admonition": {"title": "Admonition icon", "markdownDescription": "https://squidfunk.github.io/mkdocs-material/reference/admonitions/#admonition-icons", "type": "object", "properties": {"note": {"$ref": "#/$defs/icon"}, "abstract": {"$ref": "#/$defs/icon"}, "info": {"$ref": "#/$defs/icon"}, "tip": {"$ref": "#/$defs/icon"}, "success": {"$ref": "#/$defs/icon"}, "question": {"$ref": "#/$defs/icon"}, "warning": {"$ref": "#/$defs/icon"}, "failure": {"$ref": "#/$defs/icon"}, "danger": {"$ref": "#/$defs/icon"}, "bug": {"$ref": "#/$defs/icon"}, "example": {"$ref": "#/$defs/icon"}, "quote": {"$ref": "#/$defs/icon"}}, "additionalProperties": false, "defaultSnippets": [{"label": "note", "body": "note: ${1:material/pencil-circle}"}, {"label": "abstract", "body": "abstract: ${1:material/clipboard-text}"}, {"label": "info", "body": "info: ${1:material/information}"}, {"label": "tip", "body": "tip: ${1:material/fire}"}, {"label": "success", "body": "success: ${1:material/check}"}, {"label": "question", "body": "question: ${1:material/help-circle}"}, {"label": "warning", "body": "warning: ${1:material/alert}"}, {"label": "failure", "body": "failure: ${1:material/close}"}, {"label": "danger", "body": "danger: ${1:material/lightning-bolt-circle}"}, {"label": "bug", "body": "bug: ${1:material/shield-bug}"}, {"label": "example", "body": "example: ${1:material/test-tube}"}, {"label": "quote", "body": "quote: ${1:material/format-quote-close}"}]}, "tag": {"title": "Tag icon", "markdownDescription": "https://squidfunk.github.io/mkdocs-material/setup/setting-up-tags/#tag-icons-and-identifiers", "type": "object", "patternProperties": {"^[\\w-]+$": {"$ref": "#/$defs/icon"}}, "additionalProperties": false}}, "additionalProperties": false, "defaultSnippets": [{"label": "edit", "body": "edit: ${1:material/pencil}"}, {"label": "logo", "body": "logo: ${1:material/library}"}, {"label": "repo", "body": "repo: ${1:fontawesome/brands/git-alt}"}, {"label": "top", "body": "top: ${1:material/arrow-up}"}, {"label": "menu", "body": "menu: ${1:material/menu}"}, {"label": "alternate", "body": "alternate: ${1:material/translate}"}, {"label": "share", "body": "share: ${1:material/share-variant}"}, {"label": "search", "body": "search: ${1:material/magnify}"}, {"label": "previous", "body": "previous: ${1:material/arrow-left}"}, {"label": "next", "body": "next: ${1:material/arrow-right}"}]}, "features": {"title": "Feature flags", "markdownDescription": "https://squidfunk.github.io/mkdocs-material/", "type": "array", "items": {"oneOf": [{"title": "<PERSON> as read", "markdownDescription": "https://squidfunk.github.io/mkdocs-material/setup/setting-up-the-header/#mark-as-read", "const": "announce.dismiss"}, {"title": "Edit this page", "markdownDescription": "https://squidfunk.github.io/mkdocs-material/setup/adding-a-git-repository/#code-actions", "const": "content.action.edit"}, {"title": "View source of this page", "markdownDescription": "https://squidfunk.github.io/mkdocs-material/setup/adding-a-git-repository/#code-actions", "const": "content.action.view"}, {"title": "Code annotations", "markdownDescription": "https://squidfunk.github.io/mkdocs-material/reference/code-blocks/#code-annotations", "const": "content.code.annotate"}, {"title": "Code copy button", "markdownDescription": "https://squidfunk.github.io/mkdocs-material/reference/code-blocks/#code-copy-button", "const": "content.code.copy"}, {"title": "Code selection button", "markdownDescription": "https://squidfunk.github.io/mkdocs-material/reference/code-blocks/#code-selection-button", "const": "content.code.select"}, {"title": "Footnote tooltips", "markdownDescription": "https://squidfunk.github.io/mkdocs-material/reference/footnotes/#footnote-tooltips", "const": "content.footnote.tooltips"}, {"title": "Linked content tabs", "markdownDescription": "https://squidfunk.github.io/mkdocs-material/reference/content-tabs/#linked-content-tabs", "const": "content.tabs.link"}, {"title": "Improved tooltips", "markdownDescription": "https://squidfunk.github.io/mkdocs-material/reference/tooltips/#improved-tooltips", "const": "content.tooltips"}, {"title": "Header hides automatically when scrolling", "markdownDescription": "https://squidfunk.github.io/mkdocs-material/setup/setting-up-the-header/#automatic-hiding", "const": "header.autohide"}, {"title": "Navigation expansion", "markdownDescription": "https://squidfunk.github.io/mkdocs-material/setup/setting-up-navigation/#navigation-expansion", "const": "navigation.expand"}, {"title": "Navigation footer", "markdownDescription": "https://squidfunk.github.io/mkdocs-material/setup/setting-up-navigation/#navigation-footer", "const": "navigation.footer"}, {"title": "Section index pages", "markdownDescription": "https://squidfunk.github.io/mkdocs-material/setup/setting-up-navigation/#section-index-pages", "const": "navigation.indexes"}, {"title": "Instant loading", "markdownDescription": "https://squidfunk.github.io/mkdocs-material/setup/setting-up-navigation/#instant-loading", "const": "navigation.instant"}, {"title": "Instant prefetching", "markdownDescription": "https://squidfunk.github.io/mkdocs-material/setup/setting-up-navigation/#instant-prefetching", "const": "navigation.instant.prefetch"}, {"title": "Progress indicator", "markdownDescription": "https://squidfunk.github.io/mkdocs-material/setup/setting-up-navigation/#progress-indicator", "const": "navigation.instant.progress"}, {"title": "Navigation path (Breadcrumbs)", "markdownDescription": "https://squidfunk.github.io/mkdocs-material/setup/setting-up-navigation/#navigation-path", "const": "navigation.path"}, {"title": "Navigation pruning", "markdownDescription": "https://squidfunk.github.io/mkdocs-material/setup/setting-up-navigation/#navigation-pruning", "const": "navigation.prune"}, {"title": "Navigation sections", "markdownDescription": "https://squidfunk.github.io/mkdocs-material/setup/setting-up-navigation/#navigation-sections", "const": "navigation.sections"}, {"title": "Navigation tabs", "markdownDescription": "https://squidfunk.github.io/mkdocs-material/setup/setting-up-navigation/#navigation-tabs", "const": "navigation.tabs"}, {"title": "Sticky navigation tabs", "markdownDescription": "https://squidfunk.github.io/mkdocs-material/setup/setting-up-navigation/#sticky-navigation-tabs", "const": "navigation.tabs.sticky"}, {"title": "Back-to-top button", "markdownDescription": "https://squidfunk.github.io/mkdocs-material/setup/setting-up-navigation/#back-to-top-button", "const": "navigation.top"}, {"title": "Anchor tracking", "markdownDescription": "https://squidfunk.github.io/mkdocs-material/setup/setting-up-navigation/#anchor-tracking", "const": "navigation.tracking"}, {"title": "Search highlighting", "markdownDescription": "https://squidfunk.github.io/mkdocs-material/setup/setting-up-site-search/#search-highlighting", "const": "search.highlight"}, {"title": "Search sharing", "markdownDescription": "https://squidfunk.github.io/mkdocs-material/setup/setting-up-site-search/#search-sharing", "const": "search.share"}, {"title": "Search suggestions", "markdownDescription": "https://squidfunk.github.io/mkdocs-material/setup/setting-up-site-search/#search-suggestions", "const": "search.suggest"}, {"title": "Integrated table of contents", "markdownDescription": "https://squidfunk.github.io/mkdocs-material/setup/setting-up-navigation/#anchor-following", "const": "toc.follow"}, {"title": "Integrated table of contents", "markdownDescription": "https://squidfunk.github.io/mkdocs-material/setup/setting-up-navigation/#navigation-integration", "const": "toc.integrate"}]}, "uniqueItems": true, "minItems": 1}, "palette": {"oneOf": [{"title": "Color palette", "markdownDescription": "https://squidfunk.github.io/mkdocs-material/setup/changing-the-colors/", "type": "object", "properties": {"scheme": {"$ref": "#/$defs/scheme"}, "primary": {"$ref": "#/$defs/primary"}, "accent": {"$ref": "#/$defs/accent"}}, "additionalProperties": false}, {"title": "Color palette", "markdownDescription": "https://squidfunk.github.io/mkdocs-material/setup/changing-the-colors/", "type": "array", "items": {"type": "object", "properties": {"scheme": {"$ref": "#/$defs/scheme"}, "primary": {"$ref": "#/$defs/primary"}, "accent": {"$ref": "#/$defs/accent"}, "media": {"oneOf": [{"title": "System preference", "markdownDescription": "https://squidfunk.github.io/mkdocs-material/setup/changing-the-colors/#system-preference", "type": "string", "enum": ["\"(prefers-color-scheme: light)\"", "\"(prefers-color-scheme: dark)\""]}, {"type": "string"}], "defaultSnippets": [{"label": "\"(prefers-color-scheme: light)\"", "body": "\"(prefers-color-scheme: ${1:light})\""}]}, "toggle": {"title": "Color palette toggle", "markdownDescription": "https://squidfunk.github.io/mkdocs-material/setup/changing-the-colors/#color-palette-toggle", "properties": {"icon": {"$ref": "#/$defs/icon"}, "name": {"title": "Color palette toggle name", "markdownDescription": "https://squidfunk.github.io/mkdocs-material/setup/changing-the-colors/#toggle-name", "type": "string"}}, "additionalProperties": false, "required": ["icon", "name"]}}, "additionalProperties": false, "required": ["toggle"]}, "uniqueItems": true, "minItems": 1}], "default": {"scheme": "default", "primary": "indigo", "accent": "indigo"}}, "font": {"oneOf": [{"title": "Google Fonts", "markdownDescription": "https://squidfunk.github.io/mkdocs-material/setup/changing-the-fonts/", "type": "object", "properties": {"text": {"$ref": "assets/fonts.json"}, "code": {"$ref": "assets/fonts.json"}}, "additionalProperties": false}, {"title": "Disable Google Fonts", "markdownDescription": "https://squidfunk.github.io/mkdocs-material/setup/changing-the-fonts/#autoloading", "enum": [false]}], "defaultSnippets": [{"body": {"text": "${1:<PERSON><PERSON>}", "code": "${2:Roboto Mono}"}}]}}, "additionalProperties": false, "required": ["name"], "$defs": {"scheme": {"title": "Color scheme", "markdownDescription": "https://squidfunk.github.io/mkdocs-material/setup/changing-the-colors/#color-scheme", "anyOf": [{"enum": ["default", "slate"]}, {"type": "string"}]}, "primary": {"title": "Primary color", "markdownDescription": "https://squidfunk.github.io/mkdocs-material/setup/changing-the-colors/#primary-color", "enum": ["red", "pink", "purple", "deep purple", "indigo", "blue", "light blue", "cyan", "teal", "green", "light green", "lime", "yellow", "amber", "orange", "deep orange", "brown", "grey", "blue grey", "black", "white", "custom"]}, "accent": {"title": "Accent color", "markdownDescription": "https://squidfunk.github.io/mkdocs-material/setup/changing-the-colors/#accent-color", "enum": ["red", "pink", "purple", "deep purple", "indigo", "blue", "light blue", "cyan", "teal", "green", "light green", "lime", "yellow", "amber", "orange", "deep orange", "brown", "grey", "blue grey", "black", "white", "custom"]}, "icon": {"anyOf": [{"$ref": "assets/icons.json"}, {"title": "Unknown icon", "type": "string"}]}}}