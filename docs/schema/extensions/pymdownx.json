{"$schema": "https://json-schema.org/draft-07/schema", "title": "Markdown extensions", "markdownDescription": "https://squidfunk.github.io/mkdocs-material/setup/extensions/python-markdown-extensions/", "oneOf": [{"oneOf": [{"type": "object", "properties": {"pymdownx.arithmatex": {"title": "Arithmatex – Python Markdown Extensions", "markdownDescription": "https://squidfunk.github.io/mkdocs-material/setup/extensions/python-markdown-extensions/#arithmatex", "type": "object", "properties": {"generic": {"markdownDescription": "https://facelessuser.github.io/pymdown-extensions/extensions/arithmatex/#options", "type": "boolean", "default": true}}, "additionalProperties": false}}, "additionalProperties": false}, {"title": "Arithmatex – Python Markdown Extensions", "markdownDescription": "https://squidfunk.github.io/mkdocs-material/setup/extensions/python-markdown-extensions/#arithmatex", "const": "pymdownx.arithmatex"}]}, {"oneOf": [{"type": "object", "properties": {"pymdownx.betterem": {"title": "BetterEm – Python Markdown Extensions", "markdownDescription": "https://squidfunk.github.io/mkdocs-material/setup/extensions/python-markdown-extensions/#betterem", "type": "object", "properties": {"smart_enable": {"markdownDescription": "https://facelessuser.github.io/pymdown-extensions/extensions/betterem/#options", "type": "string", "enum": ["all", "asterisk", "underscore", "none"], "default": "all"}}, "additionalProperties": false}}, "additionalProperties": false}, {"title": "BetterEm – Python Markdown Extensions", "markdownDescription": "https://squidfunk.github.io/mkdocs-material/setup/extensions/python-markdown-extensions/#betterem", "const": "pymdownx.betterem"}]}, {"oneOf": [{"type": "object", "properties": {"pymdownx.blocks.caption": {"title": "Caption – Python Markdown Extensions", "markdownDescription": "https://squidfunk.github.io/mkdocs-material/setup/extensions/python-markdown-extensions/#caption", "type": "object", "properties": {"types": {"markdownDescription": "https://facelessuser.github.io/pymdown-extensions/extensions/blocks/plugins/caption/#global-options", "type": "array", "items": {"markdownDescription": "https://facelessuser.github.io/pymdown-extensions/extensions/blocks/plugins/caption/#configuring-figure-types", "oneOf": [{"markdownDescription": "https://facelessuser.github.io/pymdown-extensions/extensions/blocks/plugins/caption/#configuring-figure-types", "type": "string"}, {"markdownDescription": "https://facelessuser.github.io/pymdown-extensions/extensions/blocks/plugins/caption/#configuring-figure-types", "type": "object", "properties": {"name": {"markdownDescription": "https://facelessuser.github.io/pymdown-extensions/extensions/blocks/plugins/caption/#configuring-figure-types", "type": "string"}, "prefix": {"markdownDescription": "https://facelessuser.github.io/pymdown-extensions/extensions/blocks/plugins/caption/#configuring-figure-types", "type": "string"}}}]}, "uniqueItems": true, "minItems": 1}, "prepend": {"markdownDescription": "https://facelessuser.github.io/pymdown-extensions/extensions/blocks/plugins/caption/#global-options", "type": "boolean", "default": false}, "auto": {"markdownDescription": "https://facelessuser.github.io/pymdown-extensions/extensions/blocks/plugins/caption/#global-options", "type": "boolean", "default": true}, "auto_leval": {"markdownDescription": "https://facelessuser.github.io/pymdown-extensions/extensions/blocks/plugins/caption/#global-options", "type": "integer", "default": 0}}, "additionalProperties": false}}, "additionalProperties": false}, {"title": "Caption – Python Markdown Extensions", "markdownDescription": "https://squidfunk.github.io/mkdocs-material/setup/extensions/python-markdown-extensions/#caption", "const": "pymdownx.blocks.caption"}]}, {"oneOf": [{"title": "Caret – Python Markdown Extensions", "markdownDescription": "https://squidfunk.github.io/mkdocs-material/setup/extensions/python-markdown-extensions/#caret-mark-tilde", "const": "pymdownx.caret"}, {"type": "object", "properties": {"pymdownx.caret": {"title": "Caret – Python Markdown Extensions", "markdownDescription": "https://squidfunk.github.io/mkdocs-material/setup/extensions/python-markdown-extensions/#caret-mark-tilde", "type": "object", "properties": {"smart_insert": {"markdownDescription": "https://facelessuser.github.io/pymdown-extensions/extensions/caret/#options", "type": "boolean", "default": true}, "insert": {"markdownDescription": "https://facelessuser.github.io/pymdown-extensions/extensions/caret/#options", "type": "boolean", "default": true}, "superscript": {"markdownDescription": "https://facelessuser.github.io/pymdown-extensions/extensions/caret/#options", "type": "boolean", "default": true}}, "additionalProperties": false}}, "additionalProperties": false}]}, {"oneOf": [{"title": "Critic – Python Markdown Extensions", "markdownDescription": "https://squidfunk.github.io/mkdocs-material/setup/extensions/python-markdown-extensions/#critic", "const": "pymdownx.critic"}, {"type": "object", "properties": {"pymdownx.critic": {"title": "Critic – Python Markdown Extensions", "markdownDescription": "https://squidfunk.github.io/mkdocs-material/setup/extensions/python-markdown-extensions/#critic", "type": "object", "properties": {"mode": {"markdownDescription": "https://squidfunk.github.io/mkdocs-material/setup/extensions/python-markdown-extensions/#+pymdownx.critic.mode", "enum": ["view", "accept", "reject"]}}, "additionalProperties": false}}, "additionalProperties": false}]}, {"title": "Details – Python Markdown Extensions", "markdownDescription": "https://squidfunk.github.io/mkdocs-material/setup/extensions/python-markdown-extensions/#details", "const": "pymdownx.details"}, {"oneOf": [{"title": "Emoji – Python Markdown Extensions", "markdownDescription": "https://squidfunk.github.io/mkdocs-material/setup/extensions/python-markdown-extensions/#emoji", "const": "pymdownx.emoji"}, {"type": "object", "properties": {"pymdownx.emoji": {"title": "Emoji – Python Markdown Extensions", "markdownDescription": "https://squidfunk.github.io/mkdocs-material/setup/extensions/python-markdown-extensions/#emoji", "type": "object", "properties": {"emoji_generator": {"markdownDescription": "https://squidfunk.github.io/mkdocs-material/setup/extensions/python-markdown-extensions/#+pymdownx.emoji.emoji_generator", "default": "!!python/name:material.extensions.emoji.to_svg"}, "emoji_index": {"markdownDescription": "https://squidfunk.github.io/mkdocs-material/setup/extensions/python-markdown-extensions/#+pymdownx.emoji.emoji_index", "default": "!!python/name:material.extensions.emoji.twemoji"}, "options": {"markdownDescription": "https://squidfunk.github.io/mkdocs-material/setup/extensions/python-markdown-extensions/#+pymdownx.emoji.options.custom_icons", "type": "object", "properties": {"custom_icons": {"markdownDescription": "https://squidfunk.github.io/mkdocs-material/setup/extensions/python-markdown-extensions/#+pymdownx.emoji.options.custom_icons", "type": "array", "items": {"markdownDescription": "https://squidfunk.github.io/mkdocs-material/setup/extensions/python-markdown-extensions/#+pymdownx.emoji.options.custom_icons", "type": "string"}, "uniqueItems": true, "minItems": 1}}, "additionalProperties": false}}, "additionalProperties": false, "required": ["emoji_generator", "emoji_index"]}}, "additionalProperties": false}]}, {"oneOf": [{"title": "Highlight – Python Markdown Extensions", "markdownDescription": "https://squidfunk.github.io/mkdocs-material/setup/extensions/python-markdown-extensions/#highlight", "const": "pymdownx.highlight"}, {"type": "object", "properties": {"pymdownx.highlight": {"title": "Highlight – Python Markdown Extensions", "markdownDescription": "https://squidfunk.github.io/mkdocs-material/setup/extensions/python-markdown-extensions/#highlight", "type": "object", "properties": {"use_pygments": {"markdownDescription": "https://squidfunk.github.io/mkdocs-material/setup/extensions/python-markdown-extensions/#+pymdownx.highlight.use_pygments", "type": "boolean"}, "pygments_lang_class": {"markdownDescription": "https://squidfunk.github.io/mkdocs-material/setup/extensions/python-markdown-extensions/#+pymdownx.highlight.pygments_lang_class", "type": "boolean"}, "auto_title": {"markdownDescription": "https://squidfunk.github.io/mkdocs-material/setup/extensions/python-markdown-extensions/#+pymdownx.highlight.auto_title", "type": "boolean"}, "auto_title_map": {"markdownDescription": "https://facelessuser.github.io/pymdown-extensions/extensions/highlight/#options", "type": "object"}, "linenums": {"markdownDescription": "https://squidfunk.github.io/mkdocs-material/setup/extensions/python-markdown-extensions/#+pymdownx.highlight.linenums", "type": "boolean"}, "linenums_style": {"markdownDescription": "https://squidfunk.github.io/mkdocs-material/setup/extensions/python-markdown-extensions/#+pymdownx.highlight.linenums_style", "enum": ["inline", "pymdownx-inline", "table"]}, "anchor_linenums": {"markdownDescription": "https://squidfunk.github.io/mkdocs-material/setup/extensions/python-markdown-extensions/#+pymdownx.highlight.anchor_linenums", "type": "boolean"}, "line_spans": {"markdownDescription": "https://squidfunk.github.io/mkdocs-material/setup/extensions/python-markdown-extensions/#+pymdownx.highlight.line_spans", "type": "string"}}, "additionalProperties": false}}, "additionalProperties": false}]}, {"title": "InlineHilite – Python Markdown Extensions", "markdownDescription": "https://squidfunk.github.io/mkdocs-material/setup/extensions/python-markdown-extensions/#inlinehilite", "const": "pymdownx.inlinehilite"}, {"oneOf": [{"title": "Keys – Python Markdown Extensions", "markdownDescription": "https://squidfunk.github.io/mkdocs-material/setup/extensions/python-markdown-extensions/#keys", "const": "pymdownx.keys"}, {"type": "object", "properties": {"pymdownx.keys": {"title": "Keys – Python Markdown Extensions", "markdownDescription": "https://squidfunk.github.io/mkdocs-material/setup/extensions/python-markdown-extensions/#keys", "type": "object", "properties": {"separator": {"markdownDescription": "https://facelessuser.github.io/pymdown-extensions/extensions/mark/#options", "type": "string", "default": "+"}, "strict": {"markdownDescription": "https://facelessuser.github.io/pymdown-extensions/extensions/mark/#options", "type": "boolean", "default": false}, "camel_case": {"markdownDescription": "https://facelessuser.github.io/pymdown-extensions/extensions/mark/#options", "type": "boolean", "default": false}, "key_map": {"markdownDescription": "https://facelessuser.github.io/pymdown-extensions/extensions/mark/#options", "type": "object"}}, "additionalProperties": false}}, "additionalProperties": false}]}, {"oneOf": [{"title": "MagicLink – Python Markdown Extensions", "markdownDescription": "https://facelessuser.github.io/pymdown-extensions/extensions/magiclink/", "const": "pymdownx.magiclink"}, {"type": "object", "properties": {"pymdownx.magiclink": {"title": "MagicLink – Python Markdown Extensions", "markdownDescription": "https://facelessuser.github.io/pymdown-extensions/extensions/magiclink/", "type": "object", "properties": {"hide_protocol": {"markdownDescription": "https://facelessuser.github.io/pymdown-extensions/extensions/magiclink/#options", "type": "boolean", "default": false}, "labels": {"markdownDescription": "https://facelessuser.github.io/pymdown-extensions/extensions/magiclink/#labels", "type": "object", "default": {"commit": "Commit", "compare": "Compare", "issue": "Issue", "pull": "Pull Request", "mention": "User", "repository": "Repository"}}, "normalize_issue_symbols": {"markdownDescription": "https://facelessuser.github.io/pymdown-extensions/extensions/magiclink/#options", "type": "boolean", "default": false}, "user": {"markdownDescription": "https://facelessuser.github.io/pymdown-extensions/extensions/magiclink/#options", "type": "string", "default": ""}, "provider": {"markdownDescription": "https://facelessuser.github.io/pymdown-extensions/extensions/magiclink/#options", "type": "string", "default": "github"}, "repo": {"markdownDescription": "https://facelessuser.github.io/pymdown-extensions/extensions/magiclink/#options", "type": "string", "repo": ""}, "repo_url_shorthand": {"markdownDescription": "https://facelessuser.github.io/pymdown-extensions/extensions/magiclink/#options", "type": "boolean", "default": true}, "repo_url_shortener": {"markdownDescription": "https://facelessuser.github.io/pymdown-extensions/extensions/magiclink/#options", "type": "boolean", "default": false}, "social_url_shorthand": {"markdownDescription": "https://facelessuser.github.io/pymdown-extensions/extensions/magiclink/#options", "type": "boolean", "default": false}, "social_url_shortener": {"markdownDescription": "https://facelessuser.github.io/pymdown-extensions/extensions/magiclink/#options", "type": "boolean", "default": false}, "shortener_user_exclude": {"markdownDescription": "https://facelessuser.github.io/pymdown-extensions/extensions/magiclink/#user-excludes", "type": "object"}}, "additionalProperties": false, "required": ["user", "repo"]}}, "additionalProperties": false}]}, {"oneOf": [{"title": "Mark – Python Markdown Extensions", "markdownDescription": "https://squidfunk.github.io/mkdocs-material/setup/extensions/python-markdown-extensions/#caret-mark-tilde", "const": "pymdownx.mark"}, {"type": "object", "properties": {"pymdownx.mark": {"title": "Mark – Python Markdown Extensions", "markdownDescription": "https://squidfunk.github.io/mkdocs-material/setup/extensions/python-markdown-extensions/#caret-mark-tilde", "type": "object", "properties": {"smart_mark": {"markdownDescription": "https://facelessuser.github.io/pymdown-extensions/extensions/mark/#options", "type": "boolean", "default": true}}, "additionalProperties": false}}, "additionalProperties": false}]}, {"oneOf": [{"title": "SmartSymbols – Python Markdown Extensions", "markdownDescription": "https://squidfunk.github.io/mkdocs-material/setup/extensions/python-markdown-extensions/#smartsymbols", "const": "pymdownx.smartsymbols"}, {"type": "object", "properties": {"pymdownx.smartsymbols": {"title": "SmartSymbols – Python Markdown Extensions", "markdownDescription": "https://squidfunk.github.io/mkdocs-material/setup/extensions/python-markdown-extensions/#smartsymbols", "type": "object", "properties": {"trademark": {"markdownDescription": "https://facelessuser.github.io/pymdown-extensions/extensions/smartsymbols/#options", "type": "boolean", "default": true}, "copyright": {"markdownDescription": "https://facelessuser.github.io/pymdown-extensions/extensions/smartsymbols/#options", "type": "boolean", "default": true}, "registered": {"markdownDescription": "https://facelessuser.github.io/pymdown-extensions/extensions/smartsymbols/#options", "type": "boolean", "default": true}, "care_of": {"markdownDescription": "https://facelessuser.github.io/pymdown-extensions/extensions/smartsymbols/#options", "type": "boolean", "default": true}, "plusminus": {"markdownDescription": "https://facelessuser.github.io/pymdown-extensions/extensions/smartsymbols/#options", "type": "boolean", "default": true}, "arrows": {"markdownDescription": "https://facelessuser.github.io/pymdown-extensions/extensions/smartsymbols/#options", "type": "boolean", "default": true}, "notequal": {"markdownDescription": "https://facelessuser.github.io/pymdown-extensions/extensions/smartsymbols/#options", "type": "boolean", "default": true}, "fractions": {"markdownDescription": "https://facelessuser.github.io/pymdown-extensions/extensions/smartsymbols/#options", "type": "boolean", "default": true}, "ordinal_numbers": {"markdownDescription": "https://facelessuser.github.io/pymdown-extensions/extensions/smartsymbols/#options", "type": "boolean", "default": true}}, "additionalProperties": false}}, "additionalProperties": false}]}, {"oneOf": [{"type": "object", "properties": {"pymdownx.snippets": {"title": "Snippets – Python Markdown Extensions", "markdownDescription": "https://squidfunk.github.io/mkdocs-material/setup/extensions/python-markdown-extensions/#snippets", "type": "object", "properties": {"base_path": {"markdownDescription": "https://facelessuser.github.io/pymdown-extensions/extensions/snippets/#options", "type": "array", "items": {"markdownDescription": "https://facelessuser.github.io/pymdown-extensions/extensions/snippets/#options", "type": "string"}, "uniqueItems": true, "minItems": 1}, "auto_append": {"markdownDescription": "https://facelessuser.github.io/pymdown-extensions/extensions/snippets/#options", "type": "array", "items": {"markdownDescription": "https://facelessuser.github.io/pymdown-extensions/extensions/snippets/#options", "type": "string"}, "uniqueItems": true, "minItems": 1}, "check_paths": {"markdownDescription": "https://facelessuser.github.io/pymdown-extensions/extensions/snippets/#options", "type": "boolean", "default": true}, "url_download": {"markdownDescription": "https://facelessuser.github.io/pymdown-extensions/extensions/snippets/#options", "type": "boolean", "default": false}, "url_max_size": {"markdownDescription": "https://facelessuser.github.io/pymdown-extensions/extensions/snippets/#options", "type": "integer", "default": 33554432}, "url_timeout": {"markdownDescription": "https://facelessuser.github.io/pymdown-extensions/extensions/snippets/#options", "type": "number", "default": 10.0}, "url_request_headers": {"markdownDescription": "https://facelessuser.github.io/pymdown-extensions/extensions/snippets/#options", "type": "object", "default": {}}}, "additionalProperties": false}}, "additionalProperties": false}, {"title": "Snippets – Python Markdown Extensions", "markdownDescription": "https://squidfunk.github.io/mkdocs-material/setup/extensions/python-markdown-extensions/#snippets", "const": "pymdownx.snippets"}]}, {"oneOf": [{"title": "SuperFences – Python Markdown Extensions", "markdownDescription": "https://squidfunk.github.io/mkdocs-material/setup/extensions/python-markdown-extensions/#superfences", "const": "pymdownx.superfences"}, {"type": "object", "properties": {"pymdownx.superfences": {"title": "SuperFences – Python Markdown Extensions", "markdownDescription": "https://squidfunk.github.io/mkdocs-material/setup/extensions/python-markdown-extensions/#superfences", "type": "object", "properties": {"custom_fences": {"markdownDescription": "https://squidfunk.github.io/mkdocs-material/setup/extensions/python-markdown-extensions/#+pymdownx.superfences.custom_fences", "type": "array", "items": {"markdownDescription": "https://squidfunk.github.io/mkdocs-material/setup/extensions/python-markdown-extensions/#+pymdownx.superfences.custom_fences", "type": "object", "properties": {"name": {"markdownDescription": "https://facelessuser.github.io/pymdown-extensions/extensions/superfences/#custom-fences", "type": "string"}, "class": {"markdownDescription": "https://facelessuser.github.io/pymdown-extensions/extensions/superfences/#custom-fences", "type": "string"}, "format": {"markdownDescription": "https://facelessuser.github.io/pymdown-extensions/extensions/superfences/#custom-fences", "type": "string"}}, "additionalProperties": false}, "minItems": 1}}, "additionalProperties": false}}, "additionalProperties": false}]}, {"type": "object", "properties": {"pymdownx.tabbed": {"title": "Tabbed – Python Markdown Extensions", "markdownDescription": "https://squidfunk.github.io/mkdocs-material/setup/extensions/python-markdown-extensions/#tabbed", "type": "object", "properties": {"alternate_style": {"markdownDescription": "https://squidfunk.github.io/mkdocs-material/setup/extensions/python-markdown-extensions/#+pymdownx.tabbed.alternate_style", "type": "boolean", "enum": [true], "default": true}, "combine_header_slug": {"markdownDescription": "https://squidfunk.github.io/mkdocs-material/setup/extensions/python-markdown-extensions/#+pymdownx.tabbed.combine_header_slug", "type": "boolean", "default": true}, "slugify": {"markdownDescription": "https://squidfunk.github.io/mkdocs-material/setup/extensions/python-markdown-extensions/#+pymdownx.tabbed.slugify", "default": "!!python/object/apply:pymdownx.slugs.slugify {kwds: {case: lower}}"}}, "additionalProperties": false, "required": ["alternate_style"]}}, "additionalProperties": false}, {"oneOf": [{"title": "Tasklist – Python Markdown Extensions", "markdownDescription": "https://squidfunk.github.io/mkdocs-material/setup/extensions/python-markdown-extensions/#tasklist", "const": "pymdownx.tasklist"}, {"type": "object", "properties": {"pymdownx.tasklist": {"title": "Tasklist – Python Markdown Extensions", "markdownDescription": "https://squidfunk.github.io/mkdocs-material/setup/extensions/python-markdown-extensions/#tasklist", "type": "object", "properties": {"custom_checkbox": {"markdownDescription": "https://squidfunk.github.io/mkdocs-material/setup/extensions/python-markdown-extensions/#+pymdownx.tasklist.custom_checkbox", "type": "boolean", "default": true}, "clickable_checkbox": {"markdownDescription": "https://squidfunk.github.io/mkdocs-material/setup/extensions/python-markdown-extensions/#+pymdownx.tasklist.clickable_checkbox", "type": "boolean"}}, "additionalProperties": false}}, "additionalProperties": false}]}, {"oneOf": [{"title": "Tilde – Python Markdown Extensions", "markdownDescription": "https://squidfunk.github.io/mkdocs-material/setup/extensions/python-markdown-extensions/#caret-mark-tilde", "const": "pymdownx.tilde"}, {"type": "object", "properties": {"pymdownx.tilde": {"title": "Tilde – Python Markdown Extensions", "markdownDescription": "https://squidfunk.github.io/mkdocs-material/setup/extensions/python-markdown-extensions/#caret-mark-tilde", "type": "object", "properties": {"smart_delete": {"markdownDescription": "https://facelessuser.github.io/pymdown-extensions/extensions/tilde/#options", "type": "boolean"}, "delete": {"markdownDescription": "https://facelessuser.github.io/pymdown-extensions/extensions/tilde/#options", "type": "boolean"}, "subscript": {"markdownDescription": "https://facelessuser.github.io/pymdown-extensions/extensions/tilde/#options", "type": "boolean"}}, "additionalProperties": false}}, "additionalProperties": false}]}]}