{"$schema": "https://json-schema.org/draft-07/schema", "title": "Validation settings", "markdownDescription": "https://www.mkdocs.org/user-guide/configuration/#validation", "type": "object", "properties": {"omitted_files": {"$ref": "#/$defs/omitted_files"}, "not_found": {"$ref": "#/$defs/not_found"}, "absolute_links": {"$ref": "#/$defs/absolute_links"}, "anchors": {"$ref": "#/$defs/anchors"}, "unrecognized_links": {"$ref": "#/$defs/unrecognized_links"}, "nav": {"title": "validation of navigation", "markdownDescription": "https://www.mkdocs.org/user-guide/configuration/#validation", "properties": {"omitted_files": {"$ref": "#/$defs/omitted_files"}, "not_found": {"$ref": "#/$defs/not_found"}, "absolute_links": {"$ref": "#/$defs/absolute_links"}}, "additionalProperties": false}, "links": {"title": "Validation of links", "markdownDescription": "https://www.mkdocs.org/user-guide/configuration/#validation", "properties": {"anchors": {"$ref": "#/$defs/anchors"}, "not_found": {"$ref": "#/$defs/not_found"}, "absolute_links": {"$ref": "#/$defs/absolute_links"}, "unrecognized_links": {"$ref": "#/$defs/unrecognized_links"}}, "additionalProperties": false}}, "additionalProperties": false, "$defs": {"omitted_files": {"title": "warning level when files exist but are not referenced in navigation", "$ref": "#/$defs/warning_levels"}, "not_found": {"title": "warning level when file referenced is not found or is excluded", "$ref": "#/$defs/warning_levels"}, "absolute_links": {"title": "warning level when absolute links are used", "oneOf": [{"$ref": "#/$defs/warning_levels"}, {"enum": ["relative_to_docs"]}]}, "unrecognized_links": {"title": "warning level when a relative link cannot be resolved to a document", "$ref": "#/$defs/warning_levels"}, "anchors": {"title": "warning level when an #anchor does not exist on the linked document", "$ref": "#/$defs/warning_levels"}, "warning_levels": {"enum": ["warn", "info", "ignore"]}}}